import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { withMiddleware } from '@/lib/server/middleware';
import { db } from '@/lib/server/database';

// Interface untuk response history
export interface AssessmentHistoryItem {
  id: string;
  riasecScores: {
    R: number;
    I: number;
    A: number;
    S: number;
    E: number;
    C: number;
  };
  oceanScores: {
    O: number;
    C: number;
    E: number;
    A: number;
    N: number;
  };
  createdAt: string;
  profileGenerated: boolean;
  profileTitle?: string;
}

export interface AssessmentHistoryResponse {
  assessments: AssessmentHistoryItem[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    hasMore: boolean;
  };
}

// Handler untuk mendapatkan semua assessments (history)
async function getHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = parseInt(searchParams.get('offset') || '0');

  // Validate limit and offset
  if (limit < 1 || limit > 100) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Limit must be between 1 and 100',
      400
    );
  }

  if (offset < 0) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Offset must be non-negative',
      400
    );
  }

  try {
    // Get assessments with profiles, ordered by creation date (newest first)
    // We'll get more than needed to check if there are more items
    const assessments = await db.getRecentAssessments(limit + offset + 1);
    
    // Apply pagination
    const paginatedAssessments = assessments.slice(offset, offset + limit);
    const hasMore = assessments.length > offset + limit;
    
    // Transform to response format
    const assessmentHistory: AssessmentHistoryItem[] = paginatedAssessments.map(assessment => ({
      id: assessment.id,
      riasecScores: {
        R: assessment.riasecR,
        I: assessment.riasecI,
        A: assessment.riasecA,
        S: assessment.riasecS,
        E: assessment.riasecE,
        C: assessment.riasecC,
      },
      oceanScores: {
        O: assessment.oceanO,
        C: assessment.oceanC,
        E: assessment.oceanE,
        A: assessment.oceanA,
        N: assessment.oceanN,
      },
      createdAt: assessment.createdAt.toISOString(),
      profileGenerated: !!assessment.profile,
      profileTitle: assessment.profile?.profileTitle || undefined,
    }));

    const response: AssessmentHistoryResponse = {
      assessments: assessmentHistory,
      pagination: {
        limit,
        offset,
        total: Math.min(assessments.length, offset + limit), // Approximate total
        hasMore,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Error fetching assessment history:', error);
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to fetch assessment history',
      500
    );
  }
}

export const GET = withMiddleware(getHandler);
