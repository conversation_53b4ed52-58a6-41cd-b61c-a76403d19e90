import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  validateRiasecScores,
  validateOceanScores
} from '@/lib/api-utils';
import { AssessmentRequest, AssessmentResponse } from '@/lib/api-types';
import { db } from '@/lib/server/database';
import { withMiddleware, sanitizeInput } from '@/lib/server/middleware';

async function postHandler(request: NextRequest) {
  const rawBody = await request.json();
  const body: AssessmentRequest = sanitizeInput(rawBody);

  // Validate request body
  if (!body.riasecScores || !body.oceanScores) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Missing riasecScores or oceanScores',
      400
    );
  }

  // Validate RIASEC scores
  if (!validateRiasecScores(body.riasecScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid RIASEC scores. Each score must be between 0-30',
      400
    );
  }

  // Validate OCEAN scores
  if (!validateOceanScores(body.oceanScores)) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid OCEAN scores. Each score must be between 5-25',
      400
    );
  }

  // Create assessment in database
  const dbAssessment = await db.createAssessment(
    body.riasecScores,
    body.oceanScores,
    body.userId
  );

  const assessment: AssessmentResponse = {
    id: dbAssessment.id,
    riasecScores: body.riasecScores,
    oceanScores: body.oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: false,
  };

  return createSuccessResponse(assessment);
}

export const POST = withMiddleware(postHandler);

async function getHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const assessmentId = searchParams.get('id');

  if (!assessmentId) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Assessment ID is required',
      400
    );
  }

  // Validate assessment ID format (basic check)
  if (assessmentId.length < 10 || assessmentId.length > 50) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID format',
      400
    );
  }

  const dbAssessment = await db.getAssessmentWithScores(assessmentId);
  if (!dbAssessment) {
    return createErrorResponse(
      'NOT_FOUND',
      'Assessment not found',
      404
    );
  }

  const assessment: AssessmentResponse = {
    id: dbAssessment.id,
    riasecScores: dbAssessment.riasecScores,
    oceanScores: dbAssessment.oceanScores,
    createdAt: dbAssessment.createdAt.toISOString(),
    profileGenerated: !!dbAssessment.profile,
  };

  return createSuccessResponse(assessment);
}

export const GET = withMiddleware(getHandler);

// Handler untuk mendapatkan semua assessments (history)
async function getAllHandler(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const limit = parseInt(searchParams.get('limit') || '20');
  const offset = parseInt(searchParams.get('offset') || '0');

  // Validate limit and offset
  if (limit < 1 || limit > 100) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Limit must be between 1 and 100',
      400
    );
  }

  if (offset < 0) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Offset must be non-negative',
      400
    );
  }

  try {
    // Get assessments with profiles, ordered by creation date (newest first)
    const assessments = await db.getRecentAssessments(limit + offset);

    // Apply offset manually since we're using the existing method
    const paginatedAssessments = assessments.slice(offset, offset + limit);

    // Transform to response format
    const assessmentHistory = paginatedAssessments.map(assessment => ({
      id: assessment.id,
      riasecScores: {
        R: assessment.riasecR,
        I: assessment.riasecI,
        A: assessment.riasecA,
        S: assessment.riasecS,
        E: assessment.riasecE,
        C: assessment.riasecC,
      },
      oceanScores: {
        O: assessment.oceanO,
        C: assessment.oceanC,
        E: assessment.oceanE,
        A: assessment.oceanA,
        N: assessment.oceanN,
      },
      createdAt: assessment.createdAt.toISOString(),
      profileGenerated: !!assessment.profile,
      profileTitle: assessment.profile?.profileTitle || null,
    }));

    const response = {
      assessments: assessmentHistory,
      pagination: {
        limit,
        offset,
        total: assessments.length,
        hasMore: assessments.length > offset + limit,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Error fetching assessment history:', error);
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to fetch assessment history',
      500
    );
  }
}
