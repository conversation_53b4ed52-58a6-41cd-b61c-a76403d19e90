import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
} from '@/lib/api-utils';
import { withMiddleware } from '@/lib/server/middleware';
import { db } from '@/lib/server/database';

// Interface untuk response assessment dengan profile
export interface AssessmentWithProfileResponse {
  assessment: {
    id: string;
    riasecScores: {
      R: number;
      I: number;
      A: number;
      S: number;
      E: number;
      C: number;
    };
    oceanScores: {
      O: number;
      C: number;
      E: number;
      A: number;
      N: number;
    };
    createdAt: string;
    profileGenerated: boolean;
  };
  profile?: {
    profileTitle: string;
    profileDescription: string;
    strengths: string[];
    careerSuggestions: string[];
    workEnvironment: string;
    developmentAreas: string[];
    personalityInsights: string[];
    careerFit: string;
    generatedAt: string;
  };
}

// Handler untuk mendapatkan assessment dengan profile berdasarkan ID
async function getHandler(request: NextRequest, context?: { params: { id: string } }) {
  // Extract ID from URL if context is not available (middleware issue)
  const url = new URL(request.url);
  const pathSegments = url.pathname.split('/');
  const assessmentId = context?.params?.id || pathSegments[pathSegments.length - 1];

  // Validate assessment ID format (basic check)
  if (!assessmentId || assessmentId.length < 10 || assessmentId.length > 50) {
    return createErrorResponse(
      'VALIDATION_ERROR',
      'Invalid assessment ID format',
      400
    );
  }

  try {
    // Get assessment with profile
    const result = await db.getAssessmentWithProfile(assessmentId);
    
    if (!result || !result.assessment) {
      return createErrorResponse(
        'NOT_FOUND',
        'Assessment not found',
        404
      );
    }

    const { assessment, profile } = result;

    // Transform to response format
    const response: AssessmentWithProfileResponse = {
      assessment: {
        id: assessment.id,
        riasecScores: assessment.riasecScores,
        oceanScores: assessment.oceanScores,
        createdAt: assessment.createdAt.toISOString(),
        profileGenerated: !!profile,
      },
    };

    // Add profile if it exists
    if (profile) {
      response.profile = {
        profileTitle: profile.profileTitle,
        profileDescription: profile.profileDescription,
        strengths: profile.strengths,
        careerSuggestions: profile.careerSuggestions,
        workEnvironment: profile.workEnvironment,
        developmentAreas: profile.developmentAreas,
        personalityInsights: profile.personalityInsights,
        careerFit: profile.careerFit,
        generatedAt: profile.generatedAt.toISOString(),
      };
    }

    return createSuccessResponse(response);
  } catch (error) {
    console.error('Error fetching assessment with profile:', error);
    return createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to fetch assessment details',
      500
    );
  }
}

export const GET = withMiddleware(getHandler);
