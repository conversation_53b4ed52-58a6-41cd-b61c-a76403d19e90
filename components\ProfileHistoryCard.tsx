import Link from 'next/link';
import { AssessmentHistoryItem } from '@/lib/api-types';
import { RiasecType } from '@/lib/types';

interface ProfileHistoryCardProps {
  assessment: AssessmentHistoryItem;
}

export default function ProfileHistoryCard({ assessment }: ProfileHistoryCardProps) {
  // Calculate dominant RIASEC types
  const getDominantRiasecTypes = () => {
    const scores = assessment.riasecScores;
    const types: { type: RiasecType; score: number; name: string }[] = [
      { type: 'R', score: scores.R, name: 'Realistic' },
      { type: 'I', score: scores.I, name: 'Investigative' },
      { type: 'A', score: scores.A, name: 'Artistic' },
      { type: 'S', score: scores.S, name: 'Social' },
      { type: 'E', score: scores.E, name: 'Enterprising' },
      { type: 'C', score: scores.C, name: 'Conventional' },
    ];
    
    // Sort by score and get top 2
    return types.sort((a, b) => b.score - a.score).slice(0, 2);
  };

  // Calculate dominant OCEAN traits
  const getDominantOceanTraits = () => {
    const scores = assessment.oceanScores;
    const traits = [
      { name: 'Openness', score: scores.O },
      { name: 'Conscientiousness', score: scores.C },
      { name: 'Extraversion', score: scores.E },
      { name: 'Agreeableness', score: scores.A },
      { name: 'Neuroticism', score: scores.N },
    ];
    
    // Sort by score and get top 2
    return traits.sort((a, b) => b.score - a.score).slice(0, 2);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get time ago
  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Baru saja';
    if (diffInHours < 24) return `${diffInHours} jam yang lalu`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} hari yang lalu`;
    
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) return `${diffInWeeks} minggu yang lalu`;
    
    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} bulan yang lalu`;
  };

  const dominantRiasec = getDominantRiasecTypes();
  const dominantOcean = getDominantOceanTraits();

  return (
    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-6 text-white">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <span className="text-2xl mr-3">📊</span>
            <div>
              <h3 className="font-semibold text-lg">
                {assessment.profileGenerated && assessment.profileTitle 
                  ? assessment.profileTitle 
                  : 'Hasil Tes Kepribadian'
                }
              </h3>
              <p className="text-indigo-100 text-sm">
                {getTimeAgo(assessment.createdAt)}
              </p>
            </div>
          </div>
          <div className="text-right">
            {assessment.profileGenerated ? (
              <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                ✓ Profil Lengkap
              </span>
            ) : (
              <span className="bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                ⏳ Menunggu
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Date */}
        <div className="text-sm text-gray-500 mb-4">
          📅 {formatDate(assessment.createdAt)}
        </div>

        {/* RIASEC Summary */}
        <div className="mb-4">
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <span className="mr-2">🎯</span>
            Minat Karir Dominan
          </h4>
          <div className="flex flex-wrap gap-2">
            {dominantRiasec.map((type, index) => (
              <span 
                key={type.type}
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  index === 0 
                    ? 'bg-indigo-100 text-indigo-800' 
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {type.name} ({type.score})
              </span>
            ))}
          </div>
        </div>

        {/* OCEAN Summary */}
        <div className="mb-6">
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <span className="mr-2">🧠</span>
            Kepribadian Dominan
          </h4>
          <div className="flex flex-wrap gap-2">
            {dominantOcean.map((trait, index) => (
              <span 
                key={trait.name}
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  index === 0 
                    ? 'bg-purple-100 text-purple-800' 
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {trait.name} ({trait.score})
              </span>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <Link 
          href={`/profile/${assessment.id}`}
          className="block w-full bg-indigo-600 text-white text-center py-3 rounded-lg hover:bg-indigo-700 transition-colors group-hover:bg-indigo-700"
        >
          {assessment.profileGenerated ? 'Lihat Detail Profil' : 'Lihat Hasil Tes'}
        </Link>
      </div>
    </div>
  );
}
