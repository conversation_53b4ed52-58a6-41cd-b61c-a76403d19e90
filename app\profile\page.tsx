'use client';

import { useState, useEffect } from 'react';
import { assessmentApi } from '@/lib/client/apiService';
import { AssessmentHistoryItem } from '@/lib/api-types';
import ProfileHistoryCard from '@/components/ProfileHistoryCard';
import Link from 'next/link';

export default function ProfilePage() {
  const [assessments, setAssessments] = useState<AssessmentHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  useEffect(() => {
    loadAssessments();
  }, []);

  const loadAssessments = async (offset: number = 0) => {
    try {
      if (offset === 0) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      
      const response = await assessmentApi.getHistory(20, offset);
      
      if (offset === 0) {
        setAssessments(response.assessments);
      } else {
        setAssessments(prev => [...prev, ...response.assessments]);
      }
      
      setHasMore(response.pagination.hasMore);
      setError(null);
    } catch (err) {
      console.error('Error loading assessments:', err);
      setError('Gagal memuat riwayat tes. Silakan coba lagi.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = () => {
    if (!loadingMore && hasMore) {
      loadAssessments(assessments.length);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Memuat riwayat tes...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
              <p>{error}</p>
              <button 
                onClick={() => loadAssessments()}
                className="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
              >
                Coba Lagi
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-full p-4 shadow-lg mb-6">
            <span className="text-4xl">📊</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            Riwayat Profil Anda
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Lihat kembali hasil tes kepribadian dan minat karir yang pernah Anda lakukan
          </p>
        </div>

        {/* Navigation */}
        <div className="mb-8 text-center">
          <Link 
            href="/"
            className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mr-4"
          >
            <span className="mr-2">🏠</span>
            Kembali ke Beranda
          </Link>
          <Link 
            href="/test"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <span className="mr-2">📝</span>
            Ambil Tes Baru
          </Link>
        </div>

        {/* Assessment History */}
        {assessments.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md mx-auto">
              <span className="text-6xl mb-4 block">📝</span>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Belum Ada Riwayat Tes
              </h3>
              <p className="text-gray-600 mb-6">
                Anda belum pernah mengambil tes kepribadian dan minat karir
              </p>
              <Link 
                href="/test"
                className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                <span className="mr-2">🚀</span>
                Mulai Tes Sekarang
              </Link>
            </div>
          </div>
        ) : (
          <>
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {assessments.map((assessment) => (
                <ProfileHistoryCard 
                  key={assessment.id} 
                  assessment={assessment} 
                />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="text-center mt-8">
                <button
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingMore ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block mr-2"></div>
                      Memuat...
                    </>
                  ) : (
                    'Muat Lebih Banyak'
                  )}
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
