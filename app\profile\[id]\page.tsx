'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { assessmentApi } from '@/lib/client/apiService';
import { AssessmentWithProfileResponse } from '@/lib/api-types';
import { CombinedProfileInterpretation } from '@/lib/profileStore';
import RiasecChart from '@/components/RiasecChart';
import OceanChart from '@/components/OceanChart';
import ProfileSummary from '@/components/ProfileSummary';
import Link from 'next/link';

export default function ProfileDetailPage() {
  const params = useParams();
  const router = useRouter();
  const assessmentId = params.id as string;

  const [data, setData] = useState<AssessmentWithProfileResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (assessmentId) {
      loadAssessmentDetail();
    }
  }, [assessmentId]);

  const loadAssessmentDetail = async () => {
    try {
      setLoading(true);

      // Get assessment data with profile
      const result = await assessmentApi.getWithProfile(assessmentId);
      setData(result);
      setError(null);
    } catch (err) {
      console.error('Error loading assessment detail:', err);
      setError('Gagal memuat detail assessment. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Memuat detail profil...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg max-w-md mx-auto">
              <p>{error || 'Assessment tidak ditemukan'}</p>
              <div className="mt-4 space-x-2">
                <button 
                  onClick={() => loadAssessmentDetail()}
                  className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
                >
                  Coba Lagi
                </button>
                <Link 
                  href="/profile"
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors inline-block"
                >
                  Kembali ke Profil
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { assessment, profile } = data;

  // Convert profile to CombinedProfileInterpretation format if it exists
  const combinedProfile: CombinedProfileInterpretation | null = profile ? {
    riasecData: {
      scores: assessment.riasecScores,
      dominantTypes: [],
      level: 'Tinggi'
    },
    oceanData: {
      scores: assessment.oceanScores,
      traits: [],
      personalityType: 'Balanced'
    },
    profileTitle: profile.profileTitle,
    profileDescription: profile.profileDescription,
    strengths: profile.strengths,
    careerSuggestions: profile.careerSuggestions,
    workEnvironment: profile.workEnvironment,
    developmentAreas: profile.developmentAreas,
    personalityInsights: profile.personalityInsights,
    careerFit: profile.careerFit
  } : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-block bg-white rounded-full p-4 shadow-lg mb-4">
            <span className="text-3xl">📊</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Detail Hasil Tes
          </h1>
          <p className="text-gray-600">
            Tes dilakukan pada {formatDate(assessment.createdAt)}
          </p>
        </div>

        {/* Navigation */}
        <div className="mb-8 text-center">
          <Link
            href="/profile"
            className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors mr-4"
          >
            <span className="mr-2">←</span>
            Kembali ke Riwayat
          </Link>
          <Link
            href="/test"
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <span className="mr-2">📝</span>
            Ambil Tes Baru
          </Link>
        </div>

        {/* Profile Summary (if available) */}
        {combinedProfile && (
          <ProfileSummary
            combinedProfile={combinedProfile}
            isCombinedMode={true}
          />
        )}

        {/* Charts */}
        <div className="space-y-8">
          <RiasecChart scores={assessment.riasecScores} />
          <OceanChart scores={assessment.oceanScores} />
        </div>

        {/* Status Info */}
        <div className="mt-8 text-center">
          <div className={`inline-block px-6 py-3 rounded-lg ${
            assessment.profileGenerated
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
          }`}>
            {assessment.profileGenerated ? (
              <>
                <span className="mr-2">✅</span>
                Profil lengkap telah dihasilkan
              </>
            ) : (
              <>
                <span className="mr-2">⏳</span>
                Profil sedang diproses
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
